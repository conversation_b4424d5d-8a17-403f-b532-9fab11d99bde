<p-dialog
  (onHide)="closeDialog()"
  [(visible)]="visible"
  [closable]="true"
  [draggable]="false"
  [maximizable]="false"
  [modal]="true"
  [resizable]="false"
  [style]="{ width: '100vw', height: '100vh', maxWidth: 'none', maxHeight: 'none' }"
  styleClass="fullscreen-score-builder">

  <ng-template pTemplate="header">
    <div class="fullscreen-header">
      <div class="header-icon">
        <i class="pi pi-chart-line"></i>
      </div>
      <div class="header-title">
        <h2>Performance Score Builder</h2>
        <p>Create your perfect performance scoring formula</p>
      </div>
    </div>
  </ng-template>

  <div class="fullscreen-content">
    <!-- Top Section: Templates -->
    <div class="templates-section">
      <!-- Predefined Templates -->
      <div class="template-subsection">
        <h3>
          <i class="pi pi-bookmark"></i>
          Predefined Templates
        </h3>
        <div class="template-cards">
          <div
            (click)="selectSystemTemplate(preset)"
            *ngFor="let preset of systemConfigurations; trackBy: trackByTemplateId"
            [ngClass]="getTemplateCardClass(preset)"
            class="template-card">
            <div class="template-header">
              <h4>{{ preset.name }}
              </h4>
              <div class="template-actions">
                <p-tag
                  *ngIf="preset.is_default"
                  severity="success"
                  size="small"
                  value="Recommended">
                </p-tag>
                <button
                  (click)="setTemplateAsDefault($event, preset)"
                  [disabled]="preset.is_default"
                  [ngClass]="preset.is_default ? 'default-active-btn' : 'default-action-btn'"
                  [title]="preset.is_default ? 'Default Template' : 'Set as Default'"
                  class="custom-action-btn"
                  type="button">
                  <i class="pi pi-star"></i>
                </button>
              </div>
            </div>
            <p class="template-description">{{ preset.description }}</p>
            <div class="template-preview">
              <div
                *ngFor="let metric of getPresetPreviewMetrics(preset)"
                [style.background-color]="metric.color + '20'"
                [style.border-color]="metric.color"
                class="metric-preview">
                <span class="metric-name">{{ metric.label }}</span>
                <span class="metric-weight">{{ metric.weight }}%</span>
              </div>
              <span *ngIf="hasMoreMetrics(preset)" class="more-metrics">
                +{{ getPresetMoreMetricsCount(preset) }} more
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Custom Templates -->
      <div *ngIf="customConfigurations.length > 0" class="template-subsection">
        <div class="subsection-header">
          <h3>
            <i class="pi pi-cog"></i>
            Custom Templates
          </h3>
          <button
            (click)="clearAllCustomTemplates()"
            class="custom-action-btn delete-action-btn"
            title="Delete All Templates"
            type="button">
            <i class="pi pi-trash"></i>
          </button>
        </div>
        <div class="template-cards">
          <div
            (click)="selectCustomTemplate(custom)"
            *ngFor="let custom of customConfigurations; trackBy: trackByTemplateId"
            [ngClass]="getTemplateCardClass(custom)"
            class="template-card custom-template">
            <div class="template-header">
              <h4>{{ custom.name }}
              </h4>
              <div class="custom-actions">
                <button
                  (click)="editCustomTemplate($event, custom)"
                  class="custom-action-btn edit-action-btn"
                  title="Edit Template"
                  type="button">
                  <i class="pi pi-pencil"></i>
                </button>
                <button
                  (click)="setTemplateAsDefault($event, custom)"
                  [disabled]="custom.is_default"
                  [ngClass]="custom.is_default ? 'default-active-btn' : 'default-action-btn'"
                  [title]="custom.is_default ? 'Default Template' : 'Set as Default'"
                  class="custom-action-btn"
                  type="button">
                  <i class="pi pi-star"></i>
                </button>
                <button
                  (click)="deleteCustomTemplate($event, custom)"
                  class="custom-action-btn delete-action-btn"
                  title="Delete Template"
                  type="button">
                  <i class="pi pi-trash"></i>
                </button>
              </div>
            </div>
            <p class="template-description">{{ custom.description }}</p>
            <div class="template-preview">
              <div
                *ngFor="let metric of getPresetPreviewMetrics(custom)"
                [style.background-color]="metric.color + '20'"
                [style.border-color]="metric.color"
                class="metric-preview">
                <span class="metric-name">{{ metric.label }}</span>
                <span class="metric-weight">{{ metric.weight }}%</span>
              </div>
              <span *ngIf="hasMoreMetrics(custom)" class="more-metrics">
                +{{ getPresetMoreMetricsCount(custom) }} more
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content Grid -->
    <div class="main-grid">
      <!-- Left: Custom Templates -->
      <div class="custom-section">
        <div class="section-header">
          <h3>
            <i class="pi pi-cog"></i>
            Custom Formula
          </h3>
          <div [ngClass]="getValidationStatusClass()" class="weight-indicator">
            <span class="weight-value">{{ totalWeight }}%</span>
            <span class="weight-label">Total Weight</span>
          </div>
        </div>

        <!-- Template Creation Form -->
        <div class="template-creation-form">
          <div class="form-row">
            <div class="form-field">
              <label for="templateName">Template Name</label>
              <input
                [(ngModel)]="newTemplateName"
                class="w-full"
                id="templateName"
                pInputText
                placeholder="Enter template name"/>
            </div>
            <div class="form-field">
              <label for="templateDescription">Description</label>
              <input
                [(ngModel)]="newTemplateDescription"
                class="w-full"
                id="templateDescription"
                pInputText
                placeholder="Enter description (optional)"/>
            </div>
          </div>
          <div class="form-actions">
            <!-- Edit Mode Buttons -->
            <div *ngIf="isEditMode" class="edit-mode-actions">
              <button
                (click)="saveEditedTemplate()"
                [disabled]="!canCreateTemplate()"
                class="action-btn save-btn"
                type="button">
                <i class="pi pi-check"></i>
                <span>Save Changes</span>
              </button>
              <button
                (click)="exitEditMode()"
                class="action-btn cancel-btn"
                type="button">
                <i class="pi pi-times"></i>
                <span>Cancel</span>
              </button>
            </div>

            <!-- Create Mode Buttons -->
            <div *ngIf="!isEditMode" class="create-mode-actions">
              <button
                (click)="createCustomTemplate()"
                [disabled]="!canCreateTemplate()"
                class="action-btn create-btn"
                type="button">
                <i class="pi pi-plus"></i>
                <span>Create Template</span>
              </button>
            </div>
          </div>
        </div>

        <div class="metrics-grid">
          <div
            *ngFor="let metric of availableMetrics"
            [ngClass]="getMetricCardClass(metric)"
            class="metric-card">

            <div class="metric-info">
              <div [style.background-color]="metric.color" class="metric-icon">
                <i class="pi pi-chart-bar"></i>
              </div>
              <div class="metric-details">
                <h4>{{ metric.label }}</h4>
                <p>{{ metric.description }}</p>
              </div>
            </div>

            <div *ngIf="isMetricActive(metric)" class="metric-control">
              <div class="weight-display">
                <span class="weight-number">{{ metric.weight }}%</span>
              </div>
              <div class="weight-controls">
                <div class="weight-adjuster">
                  <button
                    (click)="decreaseWeight(metric)"
                    [disabled]="isDecreaseDisabled(metric)"
                    class="weight-btn weight-btn-minus"
                    type="button">
                    <i class="pi pi-minus"></i>
                  </button>
                  <div class="weight-input-container">
                    <p-inputNumber
                      (onInput)="onDirectWeightChange(metric, metric.weight)"
                      [(ngModel)]="metric.weight"
                      [max]="100"
                      [min]="0"
                      [showButtons]="false"
                      [step]="5"
                      size="small"
                      styleClass="compact-weight-input"
                      suffix="%">
                    </p-inputNumber>
                  </div>
                  <button
                    (click)="increaseWeight(metric)"
                    [disabled]="isIncreaseDisabled(metric)"
                    class="weight-btn weight-btn-plus"
                    type="button">
                    <i class="pi pi-plus"></i>
                  </button>
                </div>
                <button
                  (click)="removeMetric(metric)"
                  class="delete-btn"
                  title="Remove metric"
                  type="button">
                  <i class="pi pi-trash"></i>
                </button>
              </div>
            </div>

            <div *ngIf="isMetricInactive(metric)" class="metric-add">
              <button
                (click)="addMetric(metric)"
                class="add-metric-btn"
                type="button">
                <i class="pi pi-plus"></i>
                <span>Add to Formula</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Right: Live Preview -->
      <div class="preview-section">
        <div class="section-header">
          <h3>
            <i class="pi pi-eye"></i>
            Live Preview
          </h3>
          <p class="preview-subtitle">See how your formula affects real ad scores</p>
        </div>

        <div *ngIf="hasPreviewData()" class="preview-cards">
          <div
            *ngFor="let preview of previewData"
            class="preview-card">
            <!-- Ad Media -->
            <chm-ad-media
              [videoUrl]="preview.videoUrl"
              [imageUrl]="preview.imageUrl"
              [adId]="preview.adId"
              [altText]="preview.adName"
              [autoplay]="false"
              (mediaClick)="openMediaModal($event)">
            </chm-ad-media>

            <!-- Compact Preview Content -->
            <div class="compact-preview">
              <div class="ad-title">{{ preview.adName }}</div>

              <div class="metrics-comparison">
                <!-- Before Metrics -->
                <div class="metrics-column before" [ngClass]="{ 'full-width': !hasConfigurationChanged() }">
                  <div class="column-header">{{ hasConfigurationChanged() ? 'Before' : 'Current' }}</div>
                  <div class="score-badge current">{{ preview.currentScore }}%</div>
                  <div class="metrics-list">
                    <div *ngFor="let metric of getAllMetricsForPreview()" class="metric-row" [ngClass]="getMetricRowClass(metric, 'before')">
                      <span class="metric-name">{{ getShortMetricName(metric.label) }}</span>
                      <span class="metric-value">{{ getBeforeMetricValue(preview, metric) }}</span>
                    </div>
                  </div>
                </div>

                <!-- Arrow (only show if configuration changed) -->
                <div *ngIf="hasConfigurationChanged()" class="arrow-separator">
                  <i class="pi pi-arrow-right"></i>
                </div>

                <!-- After Metrics (only show if configuration changed) -->
                <div *ngIf="hasConfigurationChanged()" class="metrics-column after">
                  <div class="column-header">After</div>
                  <div class="score-badge" [ngClass]="getScoreChangeClass(preview)">{{ preview.newScore }}%</div>
                  <div class="metrics-list">
                    <div *ngFor="let metric of getAllMetricsForPreview()" class="metric-row" [ngClass]="getMetricRowClass(metric, 'after')">
                      <span class="metric-name">{{ getShortMetricName(metric.label) }}</span>
                      <span class="metric-value">{{ getAfterMetricValue(preview, metric) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div *ngIf="!hasPreviewData()" class="no-preview">
          <i class="pi pi-info-circle"></i>
          <p>No ad data available for preview</p>
        </div>
      </div>
    </div>

    <!-- Footer Actions -->
    <div class="fullscreen-footer">
      <div class="footer-left">
        <div [ngClass]="getValidationStatusClass()" class="validation-status">
          <i [ngClass]="getValidationIconClass()" class="pi"></i>
          <span>{{ getValidationMessage() }}</span>
        </div>
      </div>
      <div class="footer-actions">
        <p-button
          (onClick)="resetToDefault()"
          [outlined]="true"
          icon="pi pi-refresh"
          label="Reset"
          severity="secondary">
        </p-button>

        <p-button
          (onClick)="closeDialog()"
          [outlined]="true"
          icon="pi pi-times"
          label="Cancel"
          severity="secondary">
        </p-button>
        <p-button
          (onClick)="applyConfiguration()"
          [disabled]="!isValidConfiguration"
          icon="pi pi-check"
          label="Apply Formula"
          severity="success">
        </p-button>
      </div>
    </div>
  </div>

  <!-- Toast for notifications -->
  <p-toast position="top-right"></p-toast>

  <!-- Confirm Dialog -->
  <p-confirmDialog></p-confirmDialog>

  <!-- Media Modal -->
  <chm-media-modal
    [(visible)]="showMediaModal"
    [videoUrl]="modalVideoUrl"
    [imageUrl]="modalImageUrl"
    [adId]="modalAdId"
    [title]="modalTitle">
  </chm-media-modal>
</p-dialog>
