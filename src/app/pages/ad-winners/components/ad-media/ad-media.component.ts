import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'chm-ad-media',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="ad-media-container" (click)="onMediaClick()" [class.clickable]="hasMedia()">
      <!-- Video -->
      <video
        *ngIf="videoUrl && !videoError"
        [src]="videoUrl"
        class="ad-media-video"
        [muted]="true"
        [loop]="true"
        [autoplay]="false"
        [controls]="false"
        (error)="onVideoError()"
        (loadeddata)="onVideoLoaded()">
      </video>

      <!-- Image fallback -->
      <img
        *ngIf="(!videoUrl || videoError) && imageUrl && !imageError"
        [src]="imageUrl"
        [alt]="altText"
        class="ad-media-image"
        (error)="onImageError()"
        (load)="onImageLoaded()">

      <!-- Placeholder -->
      <div
        *ngIf="showPlaceholder()"
        class="ad-media-placeholder">
        <i class="pi pi-image"></i>
        <span>No media available</span>
      </div>

      <!-- Loading indicator -->
      <div *ngIf="isLoading" class="ad-media-loading">
        <i class="pi pi-spin pi-spinner"></i>
      </div>

      <!-- Click overlay for better UX -->
      <div *ngIf="hasMedia()" class="click-overlay">
        <i class="pi pi-search-plus"></i>
      </div>
    </div>
  `,
  styles: [`
    .ad-media-container {
      position: relative;
      width: 100%;
      height: 200px;
      border-radius: 0.5rem;
      overflow: hidden;
      background: #f8fafc;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .ad-media-video,
    .ad-media-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 0.5rem;
    }

    .ad-media-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 0.5rem;
      color: #64748b;
      font-size: 0.875rem;
    }

    .ad-media-placeholder i {
      font-size: 2rem;
      color: #cbd5e1;
    }

    .ad-media-loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #5521be;
      font-size: 1.5rem;
    }

    /* Click functionality */
    .clickable {
      cursor: pointer;
      position: relative;
    }

    .click-overlay {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(0, 0, 0, 0.7);
      color: white;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.2rem;
      opacity: 0;
      transition: opacity 0.3s ease;
      pointer-events: none;
    }

    .clickable:hover .click-overlay {
      opacity: 1;
    }

    /* Hover effects */
    .clickable:hover .ad-media-video {
      transform: scale(1.02);
      transition: transform 0.3s ease;
    }

    .clickable:hover .ad-media-image {
      transform: scale(1.02);
      transition: transform 0.3s ease;
    }
  `]
})
export class AdMediaComponent {
  @Input() videoUrl: string | null = null;
  @Input() imageUrl: string | null = null;
  @Input() altText: string = 'Ad media';
  @Input() autoplay: boolean = false;
  @Input() adId: string | null = null;

  @Output() mediaClick = new EventEmitter<{ videoUrl: string | null; imageUrl: string | null; adId: string | null }>();

  imageError = false;
  videoError = false;
  isLoading = false;

  ngOnInit(): void {
    console.log('🎬 AdMediaComponent received:', {
      videoUrl: this.videoUrl,
      imageUrl: this.imageUrl,
      altText: this.altText
    });
  }

  onVideoError(): void {
    this.videoError = true;
    this.isLoading = false;
  }

  onVideoLoaded(): void {
    this.isLoading = false;
    if (this.autoplay) {
      // Auto play video on hover or when loaded
      const video = document.querySelector('.ad-media-video') as HTMLVideoElement;
      if (video) {
        video.play().catch(() => {
          // Ignore autoplay errors
        });
      }
    }
  }

  onImageError(): void {
    this.imageError = true;
    this.isLoading = false;
  }

  onImageLoaded(): void {
    this.isLoading = false;
  }

  showPlaceholder(): boolean {
    // Show placeholder if:
    // 1. No video and no image URLs
    // 2. Video failed and no image URL
    // 3. Video failed and image failed
    // 4. No video and image failed
    return (!this.videoUrl && !this.imageUrl) ||
           (this.videoError && !this.imageUrl) ||
           (this.videoError && this.imageError) ||
           (!this.videoUrl && this.imageError);
  }

  hasMedia(): boolean {
    return !!(this.videoUrl || this.imageUrl);
  }

  onMediaClick(): void {
    if (this.hasMedia()) {
      this.mediaClick.emit({
        videoUrl: this.videoUrl,
        imageUrl: this.imageUrl
      });
    }
  }
}
