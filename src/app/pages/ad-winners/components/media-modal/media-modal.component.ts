import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DialogModule } from 'primeng/dialog';
import { ButtonModule } from 'primeng/button';

@Component({
  selector: 'chm-media-modal',
  standalone: true,
  imports: [CommonModule, DialogModule, ButtonModule],
  template: `
    <p-dialog
      [(visible)]="visible"
      (onHide)="onClose()"
      [modal]="true"
      [closable]="true"
      [draggable]="false"
      [resizable]="false"
      [maximizable]="true"
      styleClass="media-modal"
      [style]="{ width: '90vw', maxWidth: '1200px' }">
      
      <ng-template pTemplate="header">
        <div class="modal-header">
          <h3>{{ title || 'Media Preview' }}</h3>
        </div>
      </ng-template>

      <div class="media-content">
        <!-- Video -->
        <video
          *ngIf="videoUrl && !videoError"
          [src]="videoUrl"
          class="modal-video"
          [controls]="true"
          [autoplay]="true"
          [muted]="false"
          (error)="onVideoError()">
        </video>

        <!-- Image -->
        <img
          *ngIf="(!videoUrl || videoError) && imageUrl && !imageError"
          [src]="imageUrl"
          [alt]="title"
          class="modal-image"
          (error)="onImageError()">

        <!-- Error message -->
        <div *ngIf="(videoError && imageError) || (!videoUrl && !imageUrl)" class="error-message">
          <i class="pi pi-exclamation-triangle"></i>
          <p>Media could not be loaded</p>
        </div>
      </div>

      <!-- Footer buttons inside dialog content -->
      <div class="modal-footer">
        <p-button
          label="Close"
          icon="pi pi-times"
          (onClick)="onClose()"
          severity="secondary">
        </p-button>
        <p-button
          *ngIf="videoUrl || imageUrl"
          label="Open in New Tab"
          icon="pi pi-external-link"
          (onClick)="openInNewTab()"
          [outlined]="true">
        </p-button>
      </div>
    </p-dialog>
  `,
  styles: [`
    :host ::ng-deep .media-modal .p-dialog-content {
      padding: 0;
      overflow: hidden;
    }

    :host ::ng-deep .media-modal .p-dialog-header {
      padding: 1rem 1.5rem;
      border-bottom: 1px solid #e2e8f0;
    }



    .modal-header h3 {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: #1e293b;
    }

    .media-content {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 400px;
      max-height: 70vh;
      background: #000;
      position: relative;
    }

    .modal-video {
      width: 100%;
      height: auto;
      max-height: 70vh;
      object-fit: contain;
    }

    .modal-image {
      width: 100%;
      height: auto;
      max-height: 70vh;
      object-fit: contain;
    }

    .error-message {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1rem;
      color: #64748b;
      padding: 2rem;
    }

    .error-message i {
      font-size: 3rem;
      color: #ef4444;
    }

    .error-message p {
      margin: 0;
      font-size: 1.1rem;
    }

    .modal-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem 1.5rem;
      border-top: 1px solid #e2e8f0;
      background: white;
    }

    /* Responsive */
    @media (max-width: 768px) {
      :host ::ng-deep .media-modal {
        width: 95vw !important;
      }
      
      .media-content {
        min-height: 300px;
        max-height: 60vh;
      }
      
      .modal-footer {
        flex-direction: column;
        gap: 0.5rem;
      }
    }
  `]
})
export class MediaModalComponent {
  @Input() visible: boolean = false;
  @Input() videoUrl: string | null = null;
  @Input() imageUrl: string | null = null;
  @Input() title: string = '';
  @Input() adId: string | null = null;

  @Output() visibleChange = new EventEmitter<boolean>();

  videoError = false;
  imageError = false;

  onClose(): void {
    this.visible = false;
    this.visibleChange.emit(false);
    this.resetErrors();
  }

  onVideoError(): void {
    this.videoError = true;
  }

  onImageError(): void {
    this.imageError = true;
  }

  openInNewTab(): void {
    const url = this.videoUrl || this.imageUrl;
    if (url) {
      window.open(url, '_blank');
    }
  }

  private resetErrors(): void {
    this.videoError = false;
    this.imageError = false;
  }
}
