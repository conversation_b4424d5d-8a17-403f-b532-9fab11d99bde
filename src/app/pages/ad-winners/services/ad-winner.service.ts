import { Injectable } from '@angular/core';
import { catchError, combineLatest, from, map, Observable, of } from 'rxjs';
import { SupabaseService } from '../../../core/services';
import {
  AdWinner,
  AdWinnerFilters,
  AdWinnerMetric,
  AdWinnerResponse,
  FilterOption,
  WeeklyPeriod,
  PeriodOption,
  PerformanceScoreConfiguration,
  PerformanceScoreMetric,
} from '../models';
import { PerformanceScoreConfigService } from './performance-score-config.service';

@Injectable({
  providedIn: 'root',
})
export class AdWinnerService {
  constructor(
    private supabaseService: SupabaseService,
    private configService: PerformanceScoreConfigService
  ) {}

  /**
   * Get all available weekly periods (legacy - for backward compatibility)
   */
  getWeeklyPeriods(): Observable<WeeklyPeriod[]> {
    return from(
      this.supabaseService.client
        .from('view_ad_insights_weekly_periods')
        .select('*')
        .order('week_start', { ascending: false }),
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error fetching weekly periods:', response.error);
          return [];
        }
        return response.data || [];
      }),
      catchError((error) => {
        console.error('Error fetching weekly periods:', error);
        return of([]);
      }),
    );
  }

  /**
   * Get dynamic period options (7D, 14D, 30D, etc.)
   */
  getPeriodOptions(): Observable<PeriodOption[]> {
    const today = new Date();
    const options: PeriodOption[] = [
      {
        label: 'Last 7 Days (7D)',
        value: '7d',
        days: 7,
        start_date: this.formatDate(this.subtractDays(today, 7)),
        end_date: this.formatDate(today)
      },
      {
        label: 'Last 14 Days (14D)',
        value: '14d',
        days: 14,
        start_date: this.formatDate(this.subtractDays(today, 14)),
        end_date: this.formatDate(today)
      },
      {
        label: 'Last 30 Days (30D)',
        value: '30d',
        days: 30,
        start_date: this.formatDate(this.subtractDays(today, 30)),
        end_date: this.formatDate(today)
      },
      {
        label: 'Last 60 Days (60D)',
        value: '60d',
        days: 60,
        start_date: this.formatDate(this.subtractDays(today, 60)),
        end_date: this.formatDate(today)
      },
      {
        label: 'Last 90 Days (90D)',
        value: '90d',
        days: 90,
        start_date: this.formatDate(this.subtractDays(today, 90)),
        end_date: this.formatDate(today)
      }
    ];

    return of(options);
  }

  private subtractDays(date: Date, days: number): Date {
    const result = new Date(date);
    result.setDate(result.getDate() - days);
    return result;
  }

  private formatDate(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  /**
   * Get all accounts for filter dropdown
   */
  getAccounts(): Observable<FilterOption[]> {
    return from(
      this.supabaseService.client
        .from('accounts')
        .select('id, name')
        .order('name'),
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error fetching accounts:', response.error);
          return [];
        }
        return (response.data || []).map((account) => ({
          label: account.name,
          value: account.id,
        }));
      }),
      catchError((error) => {
        console.error('Error fetching accounts:', error);
        return of([]);
      }),
    );
  }

  /**
   * Get campaigns for specific account
   */
  getCampaigns(accountId?: string): Observable<FilterOption[]> {
    let query = this.supabaseService.client
      .from('campaigns')
      .select('id, name');

    if (accountId) {
      query = query.eq('account_id', accountId);
    }

    return from(query.order('name')).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error fetching campaigns:', response.error);
          return [];
        }
        return (response.data || []).map((campaign) => ({
          label: campaign.name,
          value: campaign.id,
        }));
      }),
      catchError((error) => {
        console.error('Error fetching campaigns:', error);
        return of([]);
      }),
    );
  }

  /**
   * Get ad sets for specific campaign
   */
  getAdSets(campaignId?: string): Observable<FilterOption[]> {
    let query = this.supabaseService.client.from('adsets').select('id, name');

    if (campaignId) {
      query = query.eq('campaign_id', campaignId);
    }

    return from(query.order('name')).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error fetching adsets:', response.error);
          return [];
        }
        return (response.data || []).map((adset) => ({
          label: adset.name,
          value: adset.id,
        }));
      }),
      catchError((error) => {
        console.error('Error fetching adsets:', error);
        return of([]);
      }),
    );
  }

  /**
   * Get ad winners based on filters
   */
  getAdWinners(filters: AdWinnerFilters = {}): Observable<AdWinnerResponse> {
    return combineLatest([
      this.getAdInsightsWithDetails(filters),
      this.getWeeklyPeriods(),
    ]).pipe(
      map(([insights, periods]) => {
        const adWinners = this.calculateAdWinners(insights);

        return {
          data: adWinners,
          total_count: adWinners.length,
          filters_applied: filters,
          periods_available: periods,
        };
      }),
      catchError((error) => {
        console.error('Error fetching ad winners:', error);
        return of({
          data: [],
          total_count: 0,
          filters_applied: filters,
          periods_available: [],
        });
      }),
    );
  }

  /**
   * Get ad insights with full details (ads, adsets, campaigns, accounts)
   */
  private getAdInsightsWithDetails(
    filters: AdWinnerFilters,
  ): Observable<any[]> {
    let query = this.supabaseService.client.from('ad_insights_weekly').select(`
        *,
        ads!inner(
          id,
          name,
          created_time,
          status,
          video_id,
          creative,
          videos(
            id,
            source
          ),
          adsets!inner(
            id,
            name,
            status,
            campaigns!inner(
              id,
              name,
              status,
              objective,
              accounts!inner(
                id,
                name,
                status
              )
            )
          )
        )
      `);

    // Apply filters
    if (filters.account_id) {
      query = query.eq('account_id', filters.account_id);
    }

    if (filters.campaign_id) {
      query = query.eq('campaign_id', filters.campaign_id);
    }

    if (filters.adset_id) {
      query = query.eq('adset_id', filters.adset_id);
    }

    if (filters.week_start) {
      query = query.eq('week_start', filters.week_start);
    }

    if (filters.week_end) {
      query = query.eq('week_end', filters.week_end);
    }

    if (filters.min_spend) {
      query = query.gte('total_spend', filters.min_spend);
    }

    if (filters.min_impressions) {
      query = query.gte('total_impressions', filters.min_impressions);
    }

    return from(query.order('total_spend', { ascending: false })).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error fetching ad insights:', response.error);
          return [];
        }
        return response.data || [];
      }),
    );
  }

  /**
   * Calculate ad winners based on performance metrics
   */
  private calculateAdWinners(insights: any[]): AdWinner[] {
    // Group insights by adset
    const adsetGroups = new Map<string, any[]>();

    insights.forEach((insight) => {
      const adsetId = insight.adset_id;
      if (!adsetGroups.has(adsetId)) {
        adsetGroups.set(adsetId, []);
      }
      adsetGroups.get(adsetId)!.push(insight);
    });

    const winners: AdWinner[] = [];

    // Find winner in each adset
    adsetGroups.forEach((adsetInsights, adsetId) => {
      // Sort by performance score (using default ROAS-focused calculation)
      const sortedAds = adsetInsights.sort((a, b) => {
        const aScore = this.calculateDefaultPerformanceScore(a);
        const bScore = this.calculateDefaultPerformanceScore(b);
        return bScore - aScore; // Descending order
      });

      // Create winners with ranking
      sortedAds.forEach((insight, index) => {
        const performanceScore = this.calculateDefaultPerformanceScore(insight);
        const roasValue = this.getMetricValue(insight, 'roas');

        const winner: AdWinner = {
          id: insight.ads.id,
          adset_id: insight.adset_id,
          campaign_id: insight.campaign_id,
          account_id: insight.account_id,
          name: insight.ads.name,
          created_time: insight.ads.created_time,
          status: insight.ads.status,
          video_id: insight.ads.video_id,
          video: insight.ads.videos,
          creative: insight.ads.creative || {},
          created_at: insight.ads.created_at,
          updated_at: insight.ads.updated_at,
          adset: insight.ads.adsets,
          campaign: insight.ads.adsets.campaigns,
          account: insight.ads.adsets.campaigns.accounts,
          weekly_insights: insight,
          performance_score: performanceScore,
          rank_in_adset: index + 1,
          best_metric: 'roas', // Default to ROAS
          best_metric_value: roasValue || 0,
        };

        winners.push(winner);
      });
    });

    // Sort all winners by performance score
    return winners.sort((a, b) => b.performance_score - a.performance_score);
  }

  /**
   * Get metric value from insight data
   */
  private getMetricValue(insight: any, metric: AdWinnerMetric): number | null {
    const value = insight[metric];
    return value !== null && value !== undefined && !isNaN(value)
      ? Number(value)
      : null;
  }

  /**
   * Calculate default performance score (E-commerce focused)
   */
  private calculateDefaultPerformanceScore(insight: any): number {
    // Default E-commerce focused weights
    const defaultWeights = {
      roas: 35,
      conversion_rate: 30,
      traffic_quality: 15,
      cpa: 10,
      hook_rate: 5,
      hold_rate: 5,
    };

    let score = 0;
    let totalWeight = 0;

    Object.entries(defaultWeights).forEach(([metric, weight]) => {
      const value = this.getMetricValue(insight, metric as AdWinnerMetric);
      if (value !== null) {
        let normalizedValue = 0;

        switch (metric) {
          case 'roas':
            normalizedValue = Math.min(value * 10, 100);
            break;
          case 'conversion_rate':
          case 'hook_rate':
          case 'hold_rate':
          case 'traffic_quality':
            normalizedValue = Math.min(value * 100, 100);
            break;
          case 'cpa':
            normalizedValue = Math.max(0, 100 - (value / 100) * 100);
            break;
        }

        score += normalizedValue * (weight / 100);
        totalWeight += weight;
      }
    });

    return totalWeight > 0 ? Math.round(score) : 0;
  }

  // Old calculatePerformanceScore method removed - now using configuration-based approach

  /**
   * Calculate performance score using custom configuration
   */
  calculatePerformanceScoreWithConfig(
    insight: any,
    config: PerformanceScoreConfiguration
  ): number {
    let score = 0;
    let totalWeight = 0;

    config.metrics_config.metrics.forEach(metric => {
      if (metric.weight > 0) {
        const value = this.getMetricValue(insight, metric.metric);
        if (value !== null) {
          let normalizedValue = 0;

          switch (metric.metric) {
            case 'roas':
              normalizedValue = Math.min(value * 10, 100);
              break;
            case 'conversion_rate':
            case 'hook_rate':
            case 'hold_rate':
            case 'traffic_quality':
            case 'outbound_ctr':
              normalizedValue = Math.min(value * 100, 100);
              break;
            case 'cpa':
            case 'cost_per_landing_page_view':
            case 'cost_per_add_to_cart':
              normalizedValue = Math.max(0, 100 - (value / 100) * 100);
              break;
            case 'avg_cpm':
              normalizedValue = Math.max(0, 100 - (value / 50) * 100);
              break;
            case 'total_spend':
            case 'total_impressions':
            case 'total_reach':
            case 'total_purchases':
            case 'total_purchase_value':
            case 'total_landing_page_views':
            case 'total_outbound_clicks':
            case 'total_video_plays_3s':
            case 'total_add_to_carts':
            case 'unique_outbound_clicks':
              // For volume metrics, normalize based on percentile ranking
              normalizedValue = Math.min((value / 10000) * 100, 100);
              break;
            case 'avg_frequency':
              normalizedValue = Math.min(value * 20, 100);
              break;
            case 'average_order_value':
              normalizedValue = Math.min((value / 200) * 100, 100);
              break;
          }

          score += normalizedValue * (metric.weight / 100);
          totalWeight += metric.weight;
        }
      }
    });

    return totalWeight > 0 ? Math.round(score) : 0;
  }
}
